import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { email, name, source = 'website', metadata = {} } = await req.json()

    // Validate email
    if (!email || !isValidEmail(email)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Valid email address is required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Newsletter subscription request:', { email, name, source })

    // Check if email already exists
    const { data: existingSubscriber, error: checkError } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('email', email.toLowerCase())
      .single()

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error(`Database error: ${checkError.message}`)
    }

    let subscriber
    let isNewSubscription = false

    if (existingSubscriber) {
      // Update existing subscriber
      if (existingSubscriber.status === 'unsubscribed') {
        // Resubscribe
        const { data, error } = await supabase
          .from('newsletter_subscribers')
          .update({
            status: 'active',
            name: name || existingSubscriber.name,
            source,
            metadata: { ...existingSubscriber.metadata, ...metadata },
            subscribed_at: new Date().toISOString()
          })
          .eq('id', existingSubscriber.id)
          .select()
          .single()

        if (error) throw new Error(`Failed to resubscribe: ${error.message}`)
        
        subscriber = data
        isNewSubscription = true // Treat resubscription as new for workflow purposes
        console.log('Resubscribed existing user:', email)
      } else {
        // Already subscribed
        subscriber = existingSubscriber
        console.log('User already subscribed:', email)
      }
    } else {
      // Create new subscriber
      const { data, error } = await supabase
        .from('newsletter_subscribers')
        .insert({
          email: email.toLowerCase(),
          name,
          source,
          metadata,
          status: 'active'
        })
        .select()
        .single()

      if (error) throw new Error(`Failed to create subscription: ${error.message}`)
      
      subscriber = data
      isNewSubscription = true
      console.log('Created new subscriber:', email)
    }

    // If this is a new subscription or resubscription, trigger workflows
    if (isNewSubscription) {
      try {
        const triggerData = {
          email: subscriber.email,
          name: subscriber.name,
          source: subscriber.source,
          metadata: subscriber.metadata,
          subscriber_id: subscriber.id,
          subscribed_at: subscriber.subscribed_at,
          event: 'newsletter.subscribed',
          timestamp: new Date().toISOString()
        }

        // Call the trigger-workflows function
        const response = await fetch(`${supabaseUrl}/functions/v1/trigger-workflows`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`
          },
          body: JSON.stringify({
            trigger_type: 'newsletter_signup',
            trigger_data: triggerData
          })
        })

        if (!response.ok) {
          console.error('Failed to trigger workflows:', await response.text())
        } else {
          const result = await response.json()
          console.log(`Triggered ${result.triggered_count} newsletter workflows`)
        }
      } catch (triggerError) {
        console.error('Error triggering workflows:', triggerError)
        // Don't fail the subscription if workflow triggering fails
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: isNewSubscription 
          ? 'Successfully subscribed to newsletter!' 
          : 'You are already subscribed to our newsletter.',
        subscriber: {
          id: subscriber.id,
          email: subscriber.email,
          name: subscriber.name,
          status: subscriber.status,
          subscribed_at: subscriber.subscribed_at
        },
        is_new_subscription: isNewSubscription
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error in newsletter-subscribe function:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
