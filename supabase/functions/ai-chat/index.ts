import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Helper function to detect quick support questions
function isQuickSupportQuestion(message: string): boolean {
  const quickQuestionKeywords = [
    'how do i', 'how to', 'what is', 'where is', 'can i', 'do you support',
    'pricing', 'plan', 'credit', 'limit', 'feature', 'integration', 'help',
    'support', 'contact', 'issue', 'problem', 'error', 'bug', 'question'
  ]

  const lowerMessage = message.toLowerCase()
  return quickQuestionKeywords.some(keyword => lowerMessage.includes(keyword))
}

// Helper function to detect content generation requests (chargeable)
function isContentGenerationRequest(message: string, messageType?: string): boolean {
  // Check message type first
  if (messageType === 'blog_generation' || messageType === 'workflow_help') {
    return true
  }

  // Check for blog generation keywords
  const blogKeywords = [
    'write a blog', 'create blog', 'blog post', 'article about', 'write about',
    'generate content', 'create content', 'draft a post', 'blog content'
  ]

  // Check for workflow/template generation keywords
  const workflowKeywords = [
    'create workflow', 'build workflow', 'automation template', 'zapier template',
    'workflow template', 'automate', 'trigger when', 'send email when',
    'create automation', 'build automation'
  ]

  const lowerMessage = message.toLowerCase()

  return blogKeywords.some(keyword => lowerMessage.includes(keyword)) ||
         workflowKeywords.some(keyword => lowerMessage.includes(keyword))
}

// Product knowledge base
const PRODUCT_KNOWLEDGE = `
MBI (Millennial Business Innovations) Platform Knowledge Base:

SERVICES:
- MVP Development: $5,000+, 2-4 weeks, rapid prototyping for startups
- SaaS Platform Development: $15,000+, 6-12 weeks, full-featured platforms with subscriptions
- Custom Web Applications: $8,000+, 4-8 weeks, tailored business solutions
- Technical Consulting: $150-250/hour, ongoing strategic guidance

PLATFORM FEATURES:
- Blog Management: Rich text editor, SEO tools, comments, analytics
- Workflow Automation: Visual builder, triggers, email automation, integrations
- Email Integrations: Multiple providers (Gmail, Outlook, SendGrid, etc.)
- Analytics & Reporting: Real-time tracking, performance metrics, custom reports
- User Management: Multi-tenant, role-based access, organization management

SUBSCRIPTION PLANS:
- Free: $0/month, 5 blog posts, 100 workflow credits, 50 AI credits
- Basic: $29/month, unlimited blogs, 1,000 workflow credits, 200 AI credits
- Pro: $99/month, 5,000 workflow credits, 1,000 AI credits, API access
- Enterprise: Custom pricing, unlimited everything, dedicated support

COMMON TASKS:
- Create blog post: Admin → Blog → New Post
- Set up workflow: Admin → Automations → Create Workflow
- Add email integration: Admin → Email Integrations → Add Integration
- Check credits: Admin → Credits Usage
- Upgrade plan: Admin → System Settings → Subscription

INTEGRATIONS:
- Supported: Zapier, Google Workspace, Microsoft 365, Stripe, SendGrid, Webhooks
- Coming soon: Slack, Discord, HubSpot, Salesforce, Shopify
`

interface ChatRequest {
  message: string
  context?: string
  userId: string
  organizationId: string
  model?: 'deepseek-r1' | 'gemini-flash' | 'qwen-plus' | 'auto'
  messageType?: 'chat_help' | 'blog_generation' | 'workflow_help' | 'code_assistance'
}

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string
    }
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get request data
    const { message, context, userId, organizationId, model = 'auto', messageType = 'chat_help' }: ChatRequest = await req.json()

    if (!message || !userId || !organizationId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: message, userId, organizationId' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Determine which model to use
    let selectedModel = model
    if (model === 'auto') {
      // Auto-select based on message type and content
      if (messageType === 'chat_help' || isQuickSupportQuestion(message)) {
        selectedModel = 'gemini-flash' // Fast and free for support
      } else if (messageType === 'blog_generation') {
        selectedModel = 'qwen-plus' // Good for content generation
      } else if (messageType === 'workflow_help') {
        selectedModel = 'deepseek-r1' // Best for complex reasoning
      } else {
        selectedModel = 'gemini-flash' // Default to free model
      }
    }

    // Get API keys
    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    const googleAiApiKey = Deno.env.get('GOOGLE_AI_API_KEY')

    if ((selectedModel === 'deepseek-r1' || selectedModel === 'qwen-plus') && !openRouterApiKey) {
      return new Response(
        JSON.stringify({ error: 'OpenRouter API key not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (selectedModel === 'gemini-flash' && !googleAiApiKey) {
      return new Response(
        JSON.stringify({ error: 'Google AI API key not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Determine if this request should consume credits
    const shouldChargeCredits = isContentGenerationRequest(message, messageType)

    // Check user's organization and credit limits
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('ai_credits_used, ai_credits_limit, subscription_plan')
      .eq('id', organizationId)
      .single()

    if (orgError || !organization) {
      return new Response(
        JSON.stringify({ error: 'Organization not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if user has credits remaining (only if we're charging for this request)
    if (shouldChargeCredits && organization.ai_credits_used >= organization.ai_credits_limit) {
      return new Response(
        JSON.stringify({
          error: 'AI credits limit exceeded for content generation',
          creditsUsed: organization.ai_credits_used,
          creditsLimit: organization.ai_credits_limit,
          message: 'Support questions are free, but blog and workflow generation requires credits.'
        }),
        { status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Prepare system prompt with context and product knowledge
    const systemPrompt = `You are an AI assistant for MBI (Millennial Business Innovations), a comprehensive business platform. You help users with platform support, feature guidance, and content creation.

${PRODUCT_KNOWLEDGE}

CONTENT CREATION GUIDELINES:

For BLOG POSTS (when user requests blog content):
- Start with a compelling, SEO-friendly title using # heading format
- Structure with ## subheadings for main sections
- Include: Introduction, 3-5 main points with examples, conclusion with call-to-action
- Use **bold** for key terms and *italics* for emphasis
- Add bullet points with - for lists and actionable tips
- Write 800-1200 words for comprehensive coverage
- Include practical insights and real-world examples
- End with engaging questions or next steps

For WORKFLOWS (when user requests automation/workflow):
- Clearly state workflow name and business purpose
- Define specific trigger conditions (e.g., "When new user signs up")
- Outline 3-5 logical steps with timing
- Include complete email templates with subject lines and body content
- Specify delays between actions (e.g., "Wait 24 hours")
- Provide personalization variables (e.g., {{ user.name }})
- Focus on practical business scenarios
- Make templates immediately usable

INSTRUCTIONS:
- Use the knowledge base above to answer questions about MBI's services, features, and pricing
- Be helpful, concise, and professional
- For content creation, provide complete, ready-to-use templates
- For specific technical issues, guide users to the right admin section
- If you don't know something, acknowledge it and suggest contacting support
- Keep responses focused and actionable

${context ? `Current context: ${context}` : ''}

Answer the user's question using the MBI knowledge base above.`

    // Make request to appropriate AI service
    let aiResponse: OpenRouterResponse
    let modelUsed: string

    if (selectedModel === 'gemini-flash') {
      // Call Google AI API for Gemini
      const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${googleAiApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: `${systemPrompt}\n\nUser: ${message}`
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topP: 0.9,
            maxOutputTokens: 1000,
          }
        })
      })

      if (!geminiResponse.ok) {
        const errorText = await geminiResponse.text()
        console.error('Gemini API error:', errorText)
        return new Response(
          JSON.stringify({ error: 'AI service temporarily unavailable' }),
          { status: 503, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      const geminiData = await geminiResponse.json()

      // Convert Gemini response to OpenRouter format
      aiResponse = {
        choices: [
          {
            message: {
              content: geminiData.candidates?.[0]?.content?.parts?.[0]?.text || 'No response generated'
            }
          }
        ],
        usage: {
          prompt_tokens: 0, // Gemini doesn't provide token counts
          completion_tokens: 0,
          total_tokens: 0
        }
      }
      modelUsed = 'gemini-flash'
    } else {
      // Call OpenRouter API for DeepSeek or Qwen (using FREE models)
      const modelMap = {
        'deepseek-r1': 'deepseek/deepseek-r1:free',  // Free DeepSeek R1 model
        'qwen-plus': 'qwen/qwen-2.5-72b-instruct:free'  // Free Qwen model
      }

      const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://mbi-platform.com',
          'X-Title': 'MBI Platform AI Assistant'
        },
        body: JSON.stringify({
          model: modelMap[selectedModel as keyof typeof modelMap],
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: message
            }
          ],
          max_tokens: 1000,
          temperature: 0.7,
          top_p: 0.9
        })
      })

      if (!openRouterResponse.ok) {
        const errorText = await openRouterResponse.text()
        console.error('OpenRouter API error:', errorText)
        return new Response(
          JSON.stringify({ error: 'AI service temporarily unavailable' }),
          { status: 503, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      aiResponse = await openRouterResponse.json()
      modelUsed = selectedModel
    }

    if (!aiResponse.choices || aiResponse.choices.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No response from AI service' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const responseContent = aiResponse.choices[0].message.content

    // Update AI credits usage (only if we're charging for this request)
    let creditsConsumed = 0
    if (shouldChargeCredits) {
      creditsConsumed = 1
      const { error: updateError } = await supabase
        .from('organizations')
        .update({
          ai_credits_used: organization.ai_credits_used + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', organizationId)

      if (updateError) {
        console.error('Error updating AI credits:', updateError)
        // Don't fail the request, just log the error
      }
    }

    // Log the AI interaction for analytics
    await supabase
      .from('ai_interactions')
      .insert({
        user_id: userId,
        organization_id: organizationId,
        message_type: messageType,
        input_tokens: aiResponse.usage?.prompt_tokens || 0,
        output_tokens: aiResponse.usage?.completion_tokens || 0,
        total_tokens: aiResponse.usage?.total_tokens || 0,
        model_used: modelUsed,
        credits_used: creditsConsumed,
        created_at: new Date().toISOString()
      })

    return new Response(
      JSON.stringify({
        success: true,
        response: responseContent,
        creditsUsed: organization.ai_credits_used + creditsConsumed,
        creditsLimit: organization.ai_credits_limit,
        tokensUsed: aiResponse.usage?.total_tokens || 0,
        modelUsed: modelUsed,
        chargedCredits: creditsConsumed > 0
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('AI Chat Error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
