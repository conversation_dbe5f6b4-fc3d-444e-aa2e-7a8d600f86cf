import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { trigger_type, trigger_data } = await req.json()

    console.log('Triggering workflows for:', trigger_type)

    // Find active workflows with matching trigger type
    const { data: workflows, error: workflowError } = await supabase
      .from('workflows')
      .select('*')
      .eq('active', true)
      .eq('is_template', false)

    if (workflowError) {
      throw new Error(`Failed to fetch workflows: ${workflowError.message}`)
    }

    if (!workflows || workflows.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No active workflows found',
          triggered_count: 0
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Filter workflows that have matching trigger type
    const matchingWorkflows = workflows.filter(workflow => {
      const nodes = workflow.nodes || []
      return nodes.some((node: any) => 
        node.type === 'trigger' && 
        node.data?.triggerType === trigger_type
      )
    })

    console.log(`Found ${matchingWorkflows.length} matching workflows for trigger: ${trigger_type}`)

    let successCount = 0
    let failureCount = 0
    const results = []

    // Execute each matching workflow
    for (const workflow of matchingWorkflows) {
      try {
        // Log workflow execution as pending
        const { data: execution, error: executionError } = await supabase
          .from('workflow_executions')
          .insert({
            workflow_id: workflow.id,
            workflow_name: workflow.name,
            trigger_data,
            status: 'pending',
            executed_at: new Date().toISOString()
          })
          .select()
          .single()

        if (executionError) {
          throw new Error(`Failed to log execution: ${executionError.message}`)
        }

        // Call the execute-workflow function
        const executeResponse = await fetch(`${supabaseUrl}/functions/v1/execute-workflow`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
          },
          body: JSON.stringify({
            workflow_id: workflow.id,
            execution_id: execution.id,
            trigger_data
          })
        })

        if (!executeResponse.ok) {
          const errorText = await executeResponse.text()
          throw new Error(`Workflow execution failed: ${errorText}`)
        }

        const executeResult = await executeResponse.json()
        
        results.push({
          workflow_id: workflow.id,
          workflow_name: workflow.name,
          execution_id: execution.id,
          status: 'success',
          execution_time_ms: executeResult.execution_time_ms
        })

        successCount++
        console.log(`Successfully triggered workflow: ${workflow.name}`)

      } catch (workflowError) {
        console.error(`Failed to execute workflow ${workflow.id}:`, workflowError)
        
        results.push({
          workflow_id: workflow.id,
          workflow_name: workflow.name,
          status: 'failed',
          error: workflowError.message
        })

        failureCount++
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Triggered ${successCount} workflows successfully, ${failureCount} failed`,
        triggered_count: successCount,
        failed_count: failureCount,
        total_workflows: matchingWorkflows.length,
        results
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error in trigger-workflows function:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'An unexpected error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
