import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    const body = await req.text()
    const signature = req.headers.get('stripe-signature')!
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')!

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message)
      return new Response(`Webhook Error: ${err.message}`, { status: 400 })
    }

    console.log('🎯 Received Stripe webhook:', event.type)
    console.log('Event ID:', event.id)
    console.log('Event created:', new Date(event.created * 1000).toISOString())

    // Handle the event
    switch (event.type) {
      case 'customer.subscription.created':
        console.log('📝 Processing subscription created event')
        await handleSubscriptionChange(event.data.object as Stripe.Subscription, supabaseClient)
        break

      case 'customer.subscription.updated':
        console.log('📝 Processing subscription updated event')
        await handleSubscriptionChange(event.data.object as Stripe.Subscription, supabaseClient)
        break

      case 'customer.subscription.deleted':
        console.log('🗑️ Processing subscription deleted event')
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription, supabaseClient)
        break

      case 'invoice.payment_succeeded':
        console.log('💰 Processing payment succeeded event')
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice, supabaseClient)
        break

      case 'invoice.payment_failed':
        console.log('❌ Processing payment failed event')
        await handlePaymentFailed(event.data.object as Stripe.Invoice, supabaseClient)
        break

      default:
        console.log(`⚠️ Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

async function handleSubscriptionChange(subscription: Stripe.Subscription, supabaseClient: any) {
  console.log('=== WEBHOOK DEBUG: handleSubscriptionChange ===')
  console.log('Subscription ID:', subscription.id)
  console.log('Subscription metadata:', subscription.metadata)
  console.log('Subscription status:', subscription.status)
  console.log('Customer ID:', subscription.customer)

  const organizationId = subscription.metadata.organization_id
  const userId = subscription.metadata.user_id

  console.log('Extracted organizationId:', organizationId)
  console.log('Extracted userId:', userId)

  if (!organizationId) {
    console.error('❌ No organization_id in subscription metadata')
    console.error('Available metadata keys:', Object.keys(subscription.metadata))
    return
  }

  // Get plan details from price ID
  const priceId = subscription.items.data[0]?.price.id
  console.log('Price ID:', priceId)
  const planName = getPlanNameFromPriceId(priceId)
  console.log('Plan name:', planName)

  const updateData = {
    subscription_plan: planName,
    subscription_status: subscription.status,
    stripe_subscription_id: subscription.id,
    stripe_customer_id: subscription.customer,
    ...getCreditLimitsForPlan(planName),
    credits_reset_date: new Date(subscription.current_period_end * 1000).toISOString(),
  }

  console.log('Update data for organization:', updateData)

  // Update organization subscription
  const { error: orgError, data: orgData } = await supabaseClient
    .from('organizations')
    .update(updateData)
    .eq('id', organizationId)
    .select()

  if (orgError) {
    console.error('❌ Error updating organization:', orgError)
  } else {
    console.log('✅ Organization updated successfully:', orgData)
  }

  // Update user subscription status if user_id is provided
  if (userId) {
    const { error: userError, data: userData } = await supabaseClient
      .from('profiles')
      .update({
        subscription_plan: planName,
        subscription_status: subscription.status,
      })
      .eq('id', userId)
      .select()

    if (userError) {
      console.error('❌ Error updating user profile:', userError)
    } else {
      console.log('✅ User profile updated successfully:', userData)
    }
  }

  console.log(`✅ Updated subscription for organization ${organizationId} to ${planName}`)
  console.log('=== END WEBHOOK DEBUG ===')
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription, supabaseClient: any) {
  const organizationId = subscription.metadata.organization_id
  const userId = subscription.metadata.user_id

  if (!organizationId) {
    console.error('No organization_id in subscription metadata')
    return
  }

  // Downgrade to free plan
  const { error: orgError } = await supabaseClient
    .from('organizations')
    .update({
      subscription_plan: 'free',
      subscription_status: 'cancelled',
      ...getCreditLimitsForPlan('free'),
    })
    .eq('id', organizationId)

  if (orgError) {
    console.error('Error downgrading organization:', orgError)
  }

  // Update user subscription status if user_id is provided
  if (userId) {
    const { error: userError } = await supabaseClient
      .from('profiles')
      .update({
        subscription_plan: 'free',
        subscription_status: 'cancelled',
      })
      .eq('id', userId)

    if (userError) {
      console.error('Error updating user profile:', userError)
    }
  }

  console.log(`Downgraded organization ${organizationId} to free plan`)
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice, supabaseClient: any) {
  // Reset usage credits on successful payment
  const subscriptionId = invoice.subscription as string
  
  if (subscriptionId) {
    const { error } = await supabaseClient
      .from('organizations')
      .update({
        workflow_credits_used: 0,
        ai_credits_used: 0,
        credits_reset_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      })
      .eq('stripe_subscription_id', subscriptionId)

    if (error) {
      console.error('Error resetting credits:', error)
    } else {
      console.log(`Reset credits for subscription ${subscriptionId}`)
    }
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice, supabaseClient: any) {
  const subscriptionId = invoice.subscription as string
  
  if (subscriptionId) {
    const { error } = await supabaseClient
      .from('organizations')
      .update({
        subscription_status: 'past_due',
      })
      .eq('stripe_subscription_id', subscriptionId)

    if (error) {
      console.error('Error updating subscription status:', error)
    } else {
      console.log(`Updated subscription ${subscriptionId} to past_due`)
    }
  }
}

function getPlanNameFromPriceId(priceId: string): string {
  const priceIdMap: Record<string, string> = {
    'price_1RgfpuL5UMPPQRhsR8m38Rrk': 'basic',     // Basic monthly $5
    'price_1RgfqfL5UMPPQRhsBQOLQf0U': 'basic',     // Basic annual $50
    'price_1RgfrnL5UMPPQRhsSBk994eR': 'pro',       // Pro monthly $8
    'price_1RgfsML5UMPPQRhsRtNMhjGn': 'pro',       // Pro annual $80
  }
  
  return priceIdMap[priceId] || 'free'
}

function getCreditLimitsForPlan(planName: string) {
  const limits = {
    free: {
      workflow_credits_limit: 100,
      ai_credits_limit: 50,
    },
    basic: {
      workflow_credits_limit: 1000,
      ai_credits_limit: 500,
    },
    pro: {
      workflow_credits_limit: 5000,
      ai_credits_limit: 2500,
    },
  }
  
  return limits[planName] || limits.free
}
