import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { Pricing as PricingComponent } from '@/components/ui/pricing'
import AuthModal from '@/components/auth/AuthModal'
import {
  Check,
  Zap,
  Users,
  Mail,
  Sparkles,
  Crown,
  ArrowRight,
  Star,
  Shield,
  Rocket,
  Loader2
} from 'lucide-react'
import { SUBSCRIPTION_FEATURES } from '@/types/organization'
import { toast } from 'sonner'
import { createCheckoutSession, getStripe, STRIPE_PLANS } from '@/lib/stripe'

const Pricing = () => {
  const { user, profile, loading } = useAuth()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [highlightPlan, setHighlightPlan] = useState<string | null>(null)
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null)
  const [showAuthModal, setShowAuthModal] = useState(false)

  // Get highlight and source from URL params
  const highlight = searchParams.get('highlight')
  const fromTrigger = searchParams.get('from')

  useEffect(() => {
    // Redirect unauthenticated users to home page
    if (!loading && !user) {
      navigate('/')
      return
    }

    // Set highlight based on URL params
    if (highlight) {
      setHighlightPlan('basic') // Most upsells lead to basic plan
    }
  }, [user, loading, navigate, highlight])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Rocket className="h-8 w-8 animate-pulse mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400">Loading pricing...</p>
        </div>
      </div>
    )
  }

  // Don't render anything if user is not authenticated (redirect will happen)
  if (!user) {
    return null
  }

  const currentPlan = profile?.subscription_plan || 'free'

  const handleUpgrade = async (planId: string) => {
    // Check if user is authenticated first
    if (!user) {
      setShowAuthModal(true)
      return
    }

    if (!currentOrganization) {
      toast.error('No organization found. Please try refreshing the page.')
      return
    }

    setLoadingPlan(planId)

    try {
      const stripe = await getStripe()
      if (!stripe) {
        throw new Error('Stripe not initialized')
      }

      const plan = STRIPE_PLANS[planId as keyof typeof STRIPE_PLANS]
      if (!plan) {
        throw new Error('Invalid plan selected')
      }

      console.log('Creating checkout session with organization ID:', currentOrganization.id)

      // Create checkout session
      const { sessionId } = await createCheckoutSession(
        plan.priceId,
        currentOrganization.stripe_customer_id, // Use existing customer if available
        currentOrganization.id  // Pass the actual organization ID
      )

      // Redirect to Stripe Checkout
      const { error } = await stripe.redirectToCheckout({ sessionId })

      if (error) {
        throw error
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
      toast.error('Failed to start checkout process', {
        description: 'Please try again or contact support if the issue persists.'
      })
    } finally {
      setLoadingPlan(null)
    }
  }

  // Convert SUBSCRIPTION_FEATURES to pricing component format
  const convertToPricingPlans = () => {
    try {
      return Object.entries(SUBSCRIPTION_FEATURES)
        .filter(([planId]) => planId !== 'free') // Exclude free plan from pricing component
        .map(([planId, plan]) => {
        const isCurrentPlan = currentPlan === planId
        const isHighlighted = highlightPlan === planId
        const canUpgrade = currentPlan === 'free' ||
          (currentPlan === 'basic' && ['pro', 'enterprise'].includes(planId)) ||
          (currentPlan === 'pro' && planId === 'enterprise')

        // Calculate yearly price (20% discount)
        const monthlyPrice = plan.price
        const yearlyPrice = Math.round(monthlyPrice * 0.8)

        let buttonText = 'Get Started'
        let href = '#'

        if (isCurrentPlan) {
          buttonText = 'Current Plan'
        } else if (canUpgrade) {
          // Allow all authenticated users to upgrade their subscriptions
          buttonText = loadingPlan === planId ? 'Processing...' : 'Upgrade Now'
        } else {
          buttonText = 'Contact Sales'
        }

        return {
          name: plan.name.toUpperCase(),
          price: monthlyPrice.toString(),
          yearlyPrice: yearlyPrice.toString(),
          period: 'per month',
          features: plan.features.slice(0, 8), // Limit to 8 features for better display
          description: getDescriptionForPlan(planId),
          buttonText,
          href,
          isPopular: isHighlighted || planId === 'basic', // Highlight basic plan by default
          planId, // Add planId for handling clicks
          isCurrentPlan,
          canUpgrade,
          isLoading: loadingPlan === planId
        }
      })
    } catch (error) {
      console.error('Error converting pricing plans:', error)
      return []
    }
  }

  const getDescriptionForPlan = (planId: string) => {
    const descriptions = {
      basic: 'Perfect for small teams getting started',
      pro: 'Ideal for growing businesses and teams',
      enterprise: 'For large organizations with specific needs'
    }
    return descriptions[planId as keyof typeof descriptions] || ''
  }

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free': return <Sparkles className="h-5 w-5" />
      case 'basic': return <Zap className="h-5 w-5" />
      case 'pro': return <Crown className="h-5 w-5" />
      case 'enterprise': return <Shield className="h-5 w-5" />
      default: return <Star className="h-5 w-5" />
    }
  }

  const getHighlightMessage = () => {
    if (!highlight || !fromTrigger) return null

    const messages = {
      workflow_credits: "🚀 Unlock unlimited workflow automation!",
      ai_credits: "✨ Get advanced AI assistance for your content!",
      team_features: "👥 Start collaborating with your team!",
      email_features: "📧 Supercharge your email automation!"
    }

    return messages[highlight as keyof typeof messages]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <Navigation />
      
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          {getHighlightMessage() && (
            <div className="mb-4">
              <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                {getHighlightMessage()}
              </Badge>
            </div>
          )}
          
          <h1 className="font-montserrat font-bold text-4xl md:text-5xl text-gray-900 dark:text-white mb-4">
            Internal Team Plans
          </h1>
          <p className="font-poppins text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Access to automation tools and AI features for internal projects and client work.
          </p>
          
          {/* Current Plan Indicator */}
          {currentPlan !== 'free' && (
            <div className="mt-4">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Current Plan: {SUBSCRIPTION_FEATURES[currentPlan as keyof typeof SUBSCRIPTION_FEATURES].name}
              </Badge>
            </div>
          )}
        </div>

        {/* Free Forever Banner */}
        <div className="mb-8 text-center">
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Sparkles className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold text-green-800 dark:text-green-200">
                  Team Access Only
                </h3>
              </div>
              <p className="text-green-700 dark:text-green-300 text-sm">
                These plans are for internal team access to automation and AI tools for client projects.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* New Interactive Pricing Component */}
        {(() => {
          const plans = convertToPricingPlans()
          if (!plans || plans.length === 0) {
            return (
              <div className="text-center py-8">
                <p className="text-gray-600 dark:text-gray-400">
                  Loading pricing plans...
                </p>
              </div>
            )
          }

          return (
            <PricingComponent
              plans={plans}
              title="Choose Your Plan"
              description="Unlock powerful automation and AI features. Content creation stays free forever!"
              onPlanClick={handleUpgrade}
            />
          )
        })()}

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold mb-2">Is content creation really free forever?</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes! Blog posts, stories, social features, and basic AI assistance will always be free. 
                We only charge for advanced automation and premium AI features.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Can I change plans anytime?</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Absolutely! You can upgrade or downgrade your plan at any time. 
                Changes take effect immediately with prorated billing.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What happens if I exceed my limits?</h3>
              <p className="text-gray-600 dark:text-gray-300">
                We'll notify you when you're approaching your limits and offer easy upgrade options. 
                Your content and data are always safe.
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />

      {/* Auth Modal for unauthenticated users */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  )
}

export default Pricing
